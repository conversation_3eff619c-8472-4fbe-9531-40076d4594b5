#!/usr/bin/env python3
"""
Script to run the conversation_logs table migration
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.database.connection import async_engine
from sqlalchemy import text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def run_migration():
    """Run the conversation_logs table migration"""
    
    if not async_engine:
        logger.error("Database engine not initialized")
        return False
    
    try:
        # Read the migration SQL file
        migration_file = project_root / "migrations" / "create_conversation_logs_table.sql"
        
        if not migration_file.exists():
            logger.error(f"Migration file not found: {migration_file}")
            return False
        
        with open(migration_file, 'r') as f:
            migration_sql = f.read()
        
        logger.info("Running conversation_logs table migration...")
        
        # Execute the migration
        async with async_engine.begin() as conn:
            await conn.execute(text(migration_sql))
            logger.info("✅ Migration completed successfully")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        return False


async def verify_table_creation():
    """Verify that the conversation_logs table was created successfully"""
    
    try:
        async with async_engine.begin() as conn:
            # Check if table exists
            result = await conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'conversation_logs'
                );
            """))
            
            table_exists = result.scalar()
            
            if table_exists:
                logger.info("✅ conversation_logs table exists")
                
                # Check table structure
                result = await conn.execute(text("""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns
                    WHERE table_name = 'conversation_logs'
                    ORDER BY ordinal_position;
                """))
                
                columns = result.fetchall()
                logger.info("📋 Table structure:")
                for column in columns:
                    logger.info(f"  - {column[0]}: {column[1]} (nullable: {column[2]})")
                
                # Check indexes
                result = await conn.execute(text("""
                    SELECT indexname, indexdef
                    FROM pg_indexes
                    WHERE tablename = 'conversation_logs'
                    ORDER BY indexname;
                """))
                
                indexes = result.fetchall()
                logger.info("🔍 Indexes:")
                for index in indexes:
                    logger.info(f"  - {index[0]}")
                
                return True
            else:
                logger.error("❌ conversation_logs table does not exist")
                return False
                
    except Exception as e:
        logger.error(f"❌ Verification failed: {e}")
        return False


async def main():
    """Main function"""
    logger.info("🚀 Starting conversation_logs migration...")
    
    # Run migration
    migration_success = await run_migration()
    
    if migration_success:
        # Verify table creation
        verification_success = await verify_table_creation()
        
        if verification_success:
            logger.info("🎉 Migration and verification completed successfully!")
            return 0
        else:
            logger.error("❌ Migration completed but verification failed")
            return 1
    else:
        logger.error("❌ Migration failed")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
