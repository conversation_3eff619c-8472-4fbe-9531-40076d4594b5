-- Create conversation_logs table
-- This table stores all conversation messages between users and the system

CREATE TABLE IF NOT EXISTS public.conversation_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    lead_id UUID NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
    franchisor_id UUID REFERENCES franchisors(id) ON DELETE SET NULL,
    sender TEXT NOT NULL CHECK (sender IN ('user', 'system')),
    message TEXT NOT NULL,
    message_type VARCHAR(20) NOT NULL DEFAULT 'text' CHECK (message_type IN ('text', 'media', 'image', 'video', 'audio', 'document')),
    session_id VARCHAR(100),
    phone_number VARCHAR(20),
    platform VARCHAR(20) NOT NULL DEFAULT 'sms',
    webhook_payload TEXT,
    is_active BOOLEAN DEFAULT true NOT NULL,
    is_deleted BOOLEAN DEFAULT false NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    deleted_at TIMESTAMPTZ
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_conversation_logs_lead_id ON conversation_logs(lead_id);
CREATE INDEX IF NOT EXISTS idx_conversation_logs_franchisor_id ON conversation_logs(franchisor_id);
CREATE INDEX IF NOT EXISTS idx_conversation_logs_sender ON conversation_logs(sender);
CREATE INDEX IF NOT EXISTS idx_conversation_logs_session_id ON conversation_logs(session_id);
CREATE INDEX IF NOT EXISTS idx_conversation_logs_phone_number ON conversation_logs(phone_number);
CREATE INDEX IF NOT EXISTS idx_conversation_logs_platform ON conversation_logs(platform);
CREATE INDEX IF NOT EXISTS idx_conversation_logs_message_type ON conversation_logs(message_type);
CREATE INDEX IF NOT EXISTS idx_conversation_logs_is_active ON conversation_logs(is_active);
CREATE INDEX IF NOT EXISTS idx_conversation_logs_is_deleted ON conversation_logs(is_deleted);
CREATE INDEX IF NOT EXISTS idx_conversation_logs_created_at ON conversation_logs(created_at);

-- Add trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_conversation_logs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_conversation_logs_updated_at
    BEFORE UPDATE ON conversation_logs
    FOR EACH ROW
    EXECUTE FUNCTION update_conversation_logs_updated_at();

-- Add comments for documentation
COMMENT ON TABLE conversation_logs IS 'Stores conversation messages between users and the system';
COMMENT ON COLUMN conversation_logs.id IS 'Unique identifier for the conversation log entry';
COMMENT ON COLUMN conversation_logs.lead_id IS 'Associated lead ID';
COMMENT ON COLUMN conversation_logs.franchisor_id IS 'Associated franchisor ID if available';
COMMENT ON COLUMN conversation_logs.sender IS 'Message sender: user or system';
COMMENT ON COLUMN conversation_logs.message IS 'Message content';
COMMENT ON COLUMN conversation_logs.message_type IS 'Type of message: text, media, image, video, audio, document';
COMMENT ON COLUMN conversation_logs.session_id IS 'Session identifier for grouping related messages';
COMMENT ON COLUMN conversation_logs.phone_number IS 'Phone number associated with the conversation';
COMMENT ON COLUMN conversation_logs.platform IS 'Platform where the message was sent/received';
COMMENT ON COLUMN conversation_logs.webhook_payload IS 'JSON string of original webhook payload for debugging';
COMMENT ON COLUMN conversation_logs.is_active IS 'Whether this conversation log record is active';
COMMENT ON COLUMN conversation_logs.is_deleted IS 'Whether this conversation log record is deleted';
COMMENT ON COLUMN conversation_logs.created_at IS 'When this conversation log record was created';
COMMENT ON COLUMN conversation_logs.updated_at IS 'When this conversation log record was last updated';
COMMENT ON COLUMN conversation_logs.deleted_at IS 'When this conversation log record was deleted';
