"""
Tests for conversation log functionality
"""

import pytest
import uuid
from datetime import datetime
from unittest.mock import AsyncMock, patch
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession
from app.models.conversation_log import ConversationLog
from app.models.lead import Lead
from app.models.franchisor import Franchisor
from app.schemas.conversation_log import (
    ConversationLogCreateRequest,
    SenderType,
    MessageType,
    PlatformType
)
from app.services.conversation_log_service import ConversationLogService
from app.repositories.conversation_log_repository import ConversationLogRepository


class TestConversationLogModel:
    """Test ConversationLog model"""
    
    def test_conversation_log_creation(self):
        """Test creating a ConversationLog instance"""
        lead_id = uuid.uuid4()
        franchisor_id = uuid.uuid4()
        
        conversation_log = ConversationLog(
            lead_id=lead_id,
            franchisor_id=franchisor_id,
            sender="user",
            message="Hello, I'm interested in franchise opportunities",
            message_type="text",
            session_id="sms_+1234567890",
            phone_number="+1234567890",
            platform="sms",
            webhook_payload='{"event_type": "SMS_INBOUND"}',
            is_active=True,
            is_deleted=False
        )
        
        assert conversation_log.lead_id == lead_id
        assert conversation_log.franchisor_id == franchisor_id
        assert conversation_log.sender == "user"
        assert conversation_log.message == "Hello, I'm interested in franchise opportunities"
        assert conversation_log.message_type == "text"
        assert conversation_log.session_id == "sms_+1234567890"
        assert conversation_log.phone_number == "+1234567890"
        assert conversation_log.platform == "sms"
        assert conversation_log.is_active is True
        assert conversation_log.is_deleted is False


class TestConversationLogRepository:
    """Test ConversationLogRepository"""
    
    @pytest.fixture
    def mock_db_session(self):
        """Mock database session"""
        return AsyncMock(spec=AsyncSession)
    
    @pytest.fixture
    def repository(self, mock_db_session):
        """Create repository instance"""
        return ConversationLogRepository(mock_db_session)
    
    @pytest.mark.asyncio
    async def test_create_conversation_log(self, repository, mock_db_session):
        """Test creating a conversation log"""
        lead_id = uuid.uuid4()
        
        request_data = ConversationLogCreateRequest(
            lead_id=lead_id,
            sender=SenderType.USER,
            message="Test message",
            message_type=MessageType.TEXT,
            platform=PlatformType.SMS
        )
        
        # Mock database operations
        mock_db_session.add = AsyncMock()
        mock_db_session.commit = AsyncMock()
        mock_db_session.refresh = AsyncMock()
        
        # Create conversation log
        result = await repository.create_conversation_log(request_data)
        
        # Verify database operations were called
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once()
        
        # Verify the created object
        assert result.lead_id == lead_id
        assert result.sender == "user"
        assert result.message == "Test message"
        assert result.message_type == "text"
        assert result.platform == "sms"
    
    @pytest.mark.asyncio
    async def test_sanitize_message(self, repository):
        """Test message sanitization"""
        # Test null byte removal
        message_with_null = "Hello\x00World"
        sanitized = repository._sanitize_message(message_with_null)
        assert "\x00" not in sanitized
        assert sanitized == "HelloWorld"
        
        # Test length limiting
        long_message = "A" * 15000
        sanitized = repository._sanitize_message(long_message)
        assert len(sanitized) <= 10000
        
        # Test line ending normalization
        message_with_crlf = "Hello\r\nWorld\rTest"
        sanitized = repository._sanitize_message(message_with_crlf)
        assert "\r\n" not in sanitized
        assert "\r" not in sanitized
        assert sanitized == "Hello\nWorld\nTest"


class TestConversationLogService:
    """Test ConversationLogService"""
    
    @pytest.fixture
    def mock_db_session(self):
        """Mock database session"""
        return AsyncMock(spec=AsyncSession)
    
    @pytest.fixture
    def mock_repository(self):
        """Mock repository"""
        return AsyncMock(spec=ConversationLogRepository)
    
    @pytest.fixture
    def service(self, mock_db_session, mock_repository):
        """Create service instance"""
        service = ConversationLogService(mock_db_session)
        service.repository = mock_repository
        return service
    
    @pytest.mark.asyncio
    async def test_log_user_message(self, service, mock_repository):
        """Test logging a user message"""
        lead_id = uuid.uuid4()
        message = "Hello, I'm interested in franchises"
        
        # Mock repository response
        mock_conversation_log = ConversationLog(
            id=uuid.uuid4(),
            lead_id=lead_id,
            sender="user",
            message=message,
            message_type="text",
            platform="sms",
            is_active=True,
            is_deleted=False,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        mock_repository.create_conversation_log.return_value = mock_conversation_log
        
        # Log user message
        result = await service.log_user_message(
            lead_id=lead_id,
            message=message,
            session_id="sms_+1234567890",
            phone_number="+1234567890",
            platform="sms"
        )
        
        # Verify repository was called
        mock_repository.create_conversation_log.assert_called_once()
        
        # Verify the result
        assert result.lead_id == str(lead_id)
        assert result.sender == "user"
        assert result.message == message
        assert result.platform == "sms"
    
    @pytest.mark.asyncio
    async def test_log_system_message(self, service, mock_repository):
        """Test logging a system message"""
        lead_id = uuid.uuid4()
        message = "Thank you for your interest! How can I help you today?"
        
        # Mock repository response
        mock_conversation_log = ConversationLog(
            id=uuid.uuid4(),
            lead_id=lead_id,
            sender="system",
            message=message,
            message_type="text",
            platform="sms",
            is_active=True,
            is_deleted=False,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        mock_repository.create_conversation_log.return_value = mock_conversation_log
        
        # Log system message
        result = await service.log_system_message(
            lead_id=lead_id,
            message=message,
            session_id="sms_+1234567890",
            phone_number="+1234567890",
            platform="sms"
        )
        
        # Verify repository was called
        mock_repository.create_conversation_log.assert_called_once()
        
        # Verify the result
        assert result.lead_id == str(lead_id)
        assert result.sender == "system"
        assert result.message == message
        assert result.platform == "sms"


class TestConversationLogAPI:
    """Test ConversationLog API endpoints"""
    
    @pytest.fixture
    def mock_auth_user(self):
        """Mock authenticated user"""
        return {
            "id": str(uuid.uuid4()),
            "email": "<EMAIL>",
            "role": "admin"
        }
    
    @pytest.mark.asyncio
    async def test_create_conversation_log_endpoint(self, client: TestClient, mock_auth_user):
        """Test creating a conversation log via API"""
        lead_id = uuid.uuid4()
        
        request_data = {
            "lead_id": str(lead_id),
            "sender": "user",
            "message": "Hello, I'm interested in franchise opportunities",
            "message_type": "text",
            "platform": "sms",
            "session_id": "sms_+1234567890",
            "phone_number": "+1234567890"
        }
        
        with patch('app.core.security.enhanced_auth_middleware.get_current_user', return_value=mock_auth_user):
            with patch('app.services.conversation_log_service.ConversationLogService') as mock_service:
                # Mock service response
                mock_log = ConversationLog(
                    id=uuid.uuid4(),
                    lead_id=lead_id,
                    sender="user",
                    message=request_data["message"],
                    message_type="text",
                    platform="sms",
                    is_active=True,
                    is_deleted=False,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                mock_service.return_value.repository.create_conversation_log.return_value = mock_log
                
                response = client.post("/api/v1/conversation-logs/", json=request_data)
                
                assert response.status_code == 201
                data = response.json()
                assert data["success"] is True
                assert "data" in data
    
    @pytest.mark.asyncio
    async def test_get_conversation_logs_endpoint(self, client: TestClient, mock_auth_user):
        """Test getting conversation logs via API"""
        with patch('app.core.security.enhanced_auth_middleware.get_current_user', return_value=mock_auth_user):
            with patch('app.services.conversation_log_service.ConversationLogService') as mock_service:
                # Mock service response
                mock_service.return_value.get_conversation_logs.return_value = AsyncMock()
                
                response = client.get("/api/v1/conversation-logs/")
                
                assert response.status_code == 200
                data = response.json()
                assert data["success"] is True
    
    @pytest.mark.asyncio
    async def test_get_conversation_logs_by_lead_endpoint(self, client: TestClient, mock_auth_user):
        """Test getting conversation logs by lead via API"""
        lead_id = uuid.uuid4()
        
        with patch('app.core.security.enhanced_auth_middleware.get_current_user', return_value=mock_auth_user):
            with patch('app.services.conversation_log_service.ConversationLogService') as mock_service:
                # Mock service response
                mock_service.return_value.get_conversation_logs_by_lead.return_value = []
                
                response = client.get(f"/api/v1/conversation-logs/lead/{lead_id}")
                
                assert response.status_code == 200
                data = response.json()
                assert data["success"] is True
