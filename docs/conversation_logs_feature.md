# Conversation Logs Feature

## Overview

The Conversation Logs feature provides comprehensive logging of all SMS conversations between users and the AI system through the Ku<PERSON>ity webhook handler. This feature ensures complete audit trails, conversation history tracking, and enables analytics on user interactions.

## Features

- **Automatic Message Logging**: Every incoming and outgoing SMS message is automatically logged
- **Lead Association**: Messages are linked to specific leads for complete conversation tracking
- **Session Management**: Messages are grouped by session for conversation flow analysis
- **Franchisor Context**: Optional franchisor association for business context
- **Message Sanitization**: Input validation and sanitization to prevent harmful content
- **Soft Deletion**: Records are soft-deleted for data integrity and audit purposes
- **Comprehensive API**: Full CRUD operations with authentication and authorization
- **Pagination & Filtering**: Advanced querying capabilities for conversation analysis

## Database Schema

### Table: `conversation_logs`

```sql
CREATE TABLE conversation_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    lead_id UUID NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
    franchisor_id UUID REFERENCES franchisors(id) ON DELETE SET NULL,
    sender TEXT NOT NULL CHECK (sender IN ('user', 'system')),
    message TEXT NOT NULL,
    message_type VARCHAR(20) NOT NULL DEFAULT 'text',
    session_id VARCHAR(100),
    phone_number VARCHAR(20),
    platform VARCHAR(20) NOT NULL DEFAULT 'sms',
    webhook_payload TEXT,
    is_active BOOLEAN DEFAULT true NOT NULL,
    is_deleted BOOLEAN DEFAULT false NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    deleted_at TIMESTAMPTZ
);
```

### Key Fields

- **id**: Unique identifier for each conversation log entry
- **lead_id**: Associated lead (required)
- **franchisor_id**: Associated franchisor (optional)
- **sender**: Either 'user' or 'system'
- **message**: The actual message content (sanitized)
- **message_type**: Type of message (text, media, image, video, audio, document)
- **session_id**: Groups related messages in a conversation
- **phone_number**: Phone number for SMS conversations
- **platform**: Communication platform (sms, whatsapp, etc.)
- **webhook_payload**: Original webhook data for debugging

## API Endpoints

### Authentication Required

All conversation log endpoints require authentication using Bearer tokens.

### Endpoints

#### 1. Create Conversation Log
```http
POST /api/v1/conversation-logs/
Content-Type: application/json
Authorization: Bearer <token>

{
    "lead_id": "uuid",
    "franchisor_id": "uuid",
    "sender": "user",
    "message": "Hello, I'm interested in franchises",
    "message_type": "text",
    "session_id": "sms_+1234567890",
    "phone_number": "+1234567890",
    "platform": "sms"
}
```

#### 2. List Conversation Logs
```http
GET /api/v1/conversation-logs/?skip=0&limit=20&lead_id=uuid&sender=user
Authorization: Bearer <token>
```

#### 3. Get Conversation Logs by Lead
```http
GET /api/v1/conversation-logs/lead/{lead_id}?skip=0&limit=100
Authorization: Bearer <token>
```

#### 4. Get Conversation Logs by Session
```http
GET /api/v1/conversation-logs/session/{session_id}?skip=0&limit=100
Authorization: Bearer <token>
```

#### 5. Delete Conversation Log
```http
DELETE /api/v1/conversation-logs/{conversation_log_id}
Authorization: Bearer <token>
```

## Webhook Integration

### Automatic Logging in Kudosity Webhook

The conversation logging is automatically integrated into the Kudosity SMS webhook handler (`/api/webhooks/kudosity`). When an SMS is received:

1. **User Message Logging**: The incoming SMS is logged with `sender="user"`
2. **AI Processing**: The message is processed by the conversation agent
3. **System Response Logging**: The AI response is logged with `sender="system"`
4. **Error Handling**: If logging fails, the webhook continues processing (non-blocking)

### Logging Flow

```python
# User message received via webhook
user_message = webhook_data.mo.message
lead_id = result_state.get("lead_id")

# Log user message
await conversation_log_service.log_user_message(
    lead_id=lead_id,
    message=user_message,
    session_id=session_id,
    phone_number=webhook_data.mo.sender,
    platform="sms",
    webhook_payload=payload
)

# Process with AI agent
ai_response = await agent.process_state(state)

# Log system response
await conversation_log_service.log_system_message(
    lead_id=lead_id,
    message=ai_response,
    session_id=session_id,
    phone_number=webhook_data.mo.sender,
    platform="sms",
    webhook_payload=payload
)
```

## Security Features

### Input Sanitization

All messages are sanitized before storage:
- Null bytes removed
- Control characters normalized
- Length limited to 10,000 characters
- Whitespace trimmed

### Authentication & Authorization

- All API endpoints require valid JWT tokens
- User permissions are validated through the enhanced auth middleware
- Soft deletion preserves audit trails

### Data Protection

- Webhook payloads are stored as JSON strings for debugging
- Personal data is handled according to privacy requirements
- Database constraints prevent invalid data

## Usage Examples

### Service Layer Usage

```python
from app.services.conversation_log_service import ConversationLogService

# Initialize service
service = ConversationLogService(db_session)

# Log user message
await service.log_user_message(
    lead_id=lead_uuid,
    message="I want to learn about franchises",
    session_id="sms_+1234567890",
    phone_number="+1234567890"
)

# Log system response
await service.log_system_message(
    lead_id=lead_uuid,
    message="I'd be happy to help! What type of franchise interests you?",
    session_id="sms_+1234567890",
    phone_number="+1234567890"
)

# Get conversation history
logs = await service.get_conversation_logs_by_lead(lead_uuid)
```

### Repository Layer Usage

```python
from app.repositories.conversation_log_repository import ConversationLogRepository

# Initialize repository
repo = ConversationLogRepository(db_session)

# Create log entry
log_entry = await repo.create_conversation_log(log_request)

# Get logs with filtering
logs, total = await repo.get_conversation_logs(
    skip=0,
    limit=20,
    sender="user",
    platform="sms"
)
```

## Testing

### Running Tests

```bash
# Run conversation log specific tests
pytest tests/test_conversation_logs.py -v

# Run master test suite (includes conversation logs)
python tests/master_test_suite.py
```

### Test Coverage

- Model creation and validation
- Repository CRUD operations
- Service business logic
- API endpoint functionality
- Webhook integration
- Input sanitization
- Authentication and authorization

## Migration

### Running the Migration

```bash
# Run the migration script
python scripts/run_conversation_logs_migration.py
```

### Manual Migration

```sql
-- Execute the migration SQL file
\i migrations/create_conversation_logs_table.sql
```

## Monitoring & Analytics

### Key Metrics

- Total conversations per lead
- Message volume by time period
- Response times and patterns
- User engagement metrics
- Platform usage statistics

### Queries for Analytics

```sql
-- Conversation volume by day
SELECT DATE(created_at) as date, COUNT(*) as message_count
FROM conversation_logs
WHERE is_deleted = false
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- Most active leads
SELECT lead_id, COUNT(*) as message_count
FROM conversation_logs
WHERE is_deleted = false
GROUP BY lead_id
ORDER BY message_count DESC
LIMIT 10;

-- Average conversation length
SELECT session_id, COUNT(*) as messages
FROM conversation_logs
WHERE is_deleted = false
GROUP BY session_id
ORDER BY messages DESC;
```

## Error Handling

### Logging Failures

- Database connection errors are logged but don't block webhook processing
- Invalid data is rejected with appropriate error messages
- Retry mechanisms for transient failures

### Monitoring

- Failed logging attempts are tracked in application logs
- Database health checks ensure system reliability
- Alert mechanisms for critical failures

## Future Enhancements

- **Message Threading**: Enhanced conversation flow tracking
- **Sentiment Analysis**: Automatic sentiment scoring of messages
- **Real-time Analytics**: Live conversation monitoring dashboard
- **Export Functionality**: Conversation export in various formats
- **Advanced Search**: Full-text search across conversation history
