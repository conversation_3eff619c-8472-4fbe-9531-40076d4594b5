"""
Conversation Log Service
Business logic for conversation log management
"""

from typing import Optional, List, Tuple
from uuid import UUID
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPException, status
from app.models.conversation_log import ConversationLog
from app.schemas.conversation_log import (
    ConversationLogCreateRequest,
    ConversationLogUpdateRequest,
    ConversationLogResponse,
    ConversationLogListResponse,
    SenderType,
    MessageType,
    PlatformType
)
from app.repositories.conversation_log_repository import ConversationLogRepository
import logging
import json

logger = logging.getLogger(__name__)


class ConversationLogService:
    """Service for conversation log-related business logic"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.repository = ConversationLogRepository(db)

    async def log_user_message(
        self,
        lead_id: UUID,
        message: str,
        session_id: Optional[str] = None,
        phone_number: Optional[str] = None,
        platform: str = "sms",
        franchisor_id: Optional[UUID] = None,
        webhook_payload: Optional[dict] = None
    ) -> ConversationLogResponse:
        """
        Log a message from the user.
        
        Args:
            lead_id: Associated lead ID
            message: Message content
            session_id: Session identifier
            phone_number: Phone number
            platform: Platform (sms, whatsapp, etc.)
            franchisor_id: Associated franchisor ID
            webhook_payload: Original webhook payload
            
        Returns:
            ConversationLogResponse object
        """
        try:
            # Prepare webhook payload as JSON string
            webhook_payload_str = None
            if webhook_payload:
                webhook_payload_str = json.dumps(webhook_payload)
            
            # Create conversation log request
            log_request = ConversationLogCreateRequest(
                lead_id=lead_id,
                franchisor_id=franchisor_id,
                sender=SenderType.USER,
                message=message,
                message_type=MessageType.TEXT,
                session_id=session_id,
                phone_number=phone_number,
                platform=PlatformType(platform),
                webhook_payload=webhook_payload_str
            )
            
            # Create conversation log
            conversation_log = await self.repository.create_conversation_log(log_request)
            
            # Convert to response
            return await self._conversation_log_to_response(conversation_log)
            
        except Exception as e:
            logger.error(f"Error logging user message: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to log user message"
            )

    async def log_system_message(
        self,
        lead_id: UUID,
        message: str,
        session_id: Optional[str] = None,
        phone_number: Optional[str] = None,
        platform: str = "sms",
        franchisor_id: Optional[UUID] = None,
        webhook_payload: Optional[dict] = None
    ) -> ConversationLogResponse:
        """
        Log a message from the system.
        
        Args:
            lead_id: Associated lead ID
            message: Message content
            session_id: Session identifier
            phone_number: Phone number
            platform: Platform (sms, whatsapp, etc.)
            franchisor_id: Associated franchisor ID
            webhook_payload: Original webhook payload
            
        Returns:
            ConversationLogResponse object
        """
        try:
            # Prepare webhook payload as JSON string
            webhook_payload_str = None
            if webhook_payload:
                webhook_payload_str = json.dumps(webhook_payload)
            
            # Create conversation log request
            log_request = ConversationLogCreateRequest(
                lead_id=lead_id,
                franchisor_id=franchisor_id,
                sender=SenderType.SYSTEM,
                message=message,
                message_type=MessageType.TEXT,
                session_id=session_id,
                phone_number=phone_number,
                platform=PlatformType(platform),
                webhook_payload=webhook_payload_str
            )
            
            # Create conversation log
            conversation_log = await self.repository.create_conversation_log(log_request)
            
            # Convert to response
            return await self._conversation_log_to_response(conversation_log)
            
        except Exception as e:
            logger.error(f"Error logging system message: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to log system message"
            )

    async def get_conversation_logs_by_lead(
        self,
        lead_id: UUID,
        skip: int = 0,
        limit: int = 100
    ) -> List[ConversationLogResponse]:
        """
        Get conversation logs for a specific lead.
        
        Args:
            lead_id: Lead ID to filter by
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of ConversationLogResponse objects
        """
        try:
            conversation_logs = await self.repository.get_conversation_logs_by_lead(
                lead_id=lead_id,
                skip=skip,
                limit=limit
            )
            
            return [await self._conversation_log_to_response(log) for log in conversation_logs]
            
        except Exception as e:
            logger.error(f"Error getting conversation logs for lead {lead_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve conversation logs"
            )

    async def get_conversation_logs_by_session(
        self,
        session_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[ConversationLogResponse]:
        """
        Get conversation logs for a specific session.
        
        Args:
            session_id: Session ID to filter by
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of ConversationLogResponse objects
        """
        try:
            conversation_logs = await self.repository.get_conversation_logs_by_session(
                session_id=session_id,
                skip=skip,
                limit=limit
            )
            
            return [await self._conversation_log_to_response(log) for log in conversation_logs]
            
        except Exception as e:
            logger.error(f"Error getting conversation logs for session {session_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve conversation logs"
            )

    async def get_conversation_logs(
        self,
        skip: int = 0,
        limit: int = 20,
        lead_id: Optional[UUID] = None,
        sender: Optional[str] = None,
        platform: Optional[str] = None,
        message_type: Optional[str] = None,
        created_from: Optional[datetime] = None,
        created_to: Optional[datetime] = None
    ) -> ConversationLogListResponse:
        """
        Get conversation logs with filtering and pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            lead_id: Filter by lead ID
            sender: Filter by sender type
            platform: Filter by platform
            message_type: Filter by message type
            created_from: Filter by creation date from
            created_to: Filter by creation date to
            
        Returns:
            ConversationLogListResponse object
        """
        try:
            conversation_logs, total = await self.repository.get_conversation_logs(
                skip=skip,
                limit=limit,
                lead_id=lead_id,
                sender=sender,
                platform=platform,
                message_type=message_type,
                created_from=created_from,
                created_to=created_to
            )
            
            # Convert to responses
            log_responses = [await self._conversation_log_to_response(log) for log in conversation_logs]
            
            # Calculate pagination
            page = (skip // limit) + 1
            total_pages = (total + limit - 1) // limit
            
            return ConversationLogListResponse(
                conversation_logs=log_responses,
                total=total,
                page=page,
                per_page=limit,
                total_pages=total_pages
            )
            
        except Exception as e:
            logger.error(f"Error getting conversation logs: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve conversation logs"
            )

    async def delete_conversation_log(self, conversation_log_id: UUID) -> bool:
        """
        Soft delete a conversation log entry.
        
        Args:
            conversation_log_id: ID of the conversation log to delete
            
        Returns:
            True if successful
        """
        try:
            success = await self.repository.soft_delete_conversation_log(conversation_log_id)
            
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Conversation log not found"
                )
            
            return success
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting conversation log {conversation_log_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete conversation log"
            )

    async def _conversation_log_to_response(self, conversation_log: ConversationLog) -> ConversationLogResponse:
        """Convert ConversationLog model to ConversationLogResponse"""
        return ConversationLogResponse(
            id=str(conversation_log.id),
            lead_id=str(conversation_log.lead_id),
            franchisor_id=str(conversation_log.franchisor_id) if conversation_log.franchisor_id else None,
            sender=conversation_log.sender,
            message=conversation_log.message,
            message_type=conversation_log.message_type,
            session_id=conversation_log.session_id,
            phone_number=conversation_log.phone_number,
            platform=conversation_log.platform,
            webhook_payload=conversation_log.webhook_payload,
            is_active=conversation_log.is_active,
            is_deleted=conversation_log.is_deleted,
            created_at=conversation_log.created_at,
            updated_at=conversation_log.updated_at,
            deleted_at=conversation_log.deleted_at
        )
