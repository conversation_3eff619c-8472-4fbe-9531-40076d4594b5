"""
Conversation Log Schemas
Pydantic models for conversation log API requests and responses
"""

from datetime import datetime
from typing import Optional
from uuid import UUID
from pydantic import BaseModel, Field, ConfigDict
from enum import Enum


class SenderType(str, Enum):
    """Valid sender types"""
    USER = "user"
    SYSTEM = "system"


class MessageType(str, Enum):
    """Valid message types"""
    TEXT = "text"
    MEDIA = "media"
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    DOCUMENT = "document"


class PlatformType(str, Enum):
    """Valid platform types"""
    SMS = "sms"
    WHATSAPP = "whatsapp"
    WEBHOOK = "webhook"


class ConversationLogCreateRequest(BaseModel):
    """Schema for creating a conversation log entry"""
    
    lead_id: UUID = Field(..., description="Associated lead ID")
    franchisor_id: Optional[UUID] = Field(None, description="Associated franchisor ID if available")
    sender: SenderType = Field(..., description="Message sender: 'user' or 'system'")
    message: str = Field(..., min_length=1, max_length=10000, description="Message content")
    message_type: MessageType = Field(MessageType.TEXT, description="Message type")
    session_id: Optional[str] = Field(None, max_length=100, description="Session identifier")
    phone_number: Optional[str] = Field(None, max_length=20, description="Phone number")
    platform: PlatformType = Field(PlatformType.SMS, description="Platform")
    webhook_payload: Optional[str] = Field(None, description="JSON string of webhook payload")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "lead_id": "550e8400-e29b-41d4-a716-************",
                "franchisor_id": "550e8400-e29b-41d4-a716-************",
                "sender": "user",
                "message": "Hello, I'm interested in franchise opportunities",
                "message_type": "text",
                "session_id": "sms_+1234567890",
                "phone_number": "+1234567890",
                "platform": "sms",
                "webhook_payload": "{\"event_type\": \"SMS_INBOUND\"}"
            }
        }
    )


class ConversationLogUpdateRequest(BaseModel):
    """Schema for updating a conversation log entry"""
    
    message: Optional[str] = Field(None, min_length=1, max_length=10000, description="Message content")
    message_type: Optional[MessageType] = Field(None, description="Message type")
    is_active: Optional[bool] = Field(None, description="Whether the record is active")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "message": "Updated message content",
                "message_type": "text",
                "is_active": True
            }
        }
    )


class ConversationLogResponse(BaseModel):
    """Schema for conversation log response"""
    
    id: str = Field(..., description="Conversation log ID")
    lead_id: str = Field(..., description="Associated lead ID")
    franchisor_id: Optional[str] = Field(None, description="Associated franchisor ID")
    sender: str = Field(..., description="Message sender")
    message: str = Field(..., description="Message content")
    message_type: str = Field(..., description="Message type")
    session_id: Optional[str] = Field(None, description="Session identifier")
    phone_number: Optional[str] = Field(None, description="Phone number")
    platform: str = Field(..., description="Platform")
    webhook_payload: Optional[str] = Field(None, description="Webhook payload")
    is_active: bool = Field(..., description="Whether the record is active")
    is_deleted: bool = Field(..., description="Whether the record is deleted")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    deleted_at: Optional[datetime] = Field(None, description="Deletion timestamp")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": "550e8400-e29b-41d4-a716-************",
                "lead_id": "550e8400-e29b-41d4-a716-************",
                "franchisor_id": "550e8400-e29b-41d4-a716-446655440002",
                "sender": "user",
                "message": "Hello, I'm interested in franchise opportunities",
                "message_type": "text",
                "session_id": "sms_+1234567890",
                "phone_number": "+1234567890",
                "platform": "sms",
                "webhook_payload": "{\"event_type\": \"SMS_INBOUND\"}",
                "is_active": True,
                "is_deleted": False,
                "created_at": "2024-01-01T12:00:00Z",
                "updated_at": "2024-01-01T12:00:00Z",
                "deleted_at": None
            }
        }
    )


class ConversationLogListResponse(BaseModel):
    """Schema for conversation log list response"""
    
    conversation_logs: list[ConversationLogResponse] = Field(..., description="List of conversation logs")
    total: int = Field(..., description="Total number of conversation logs")
    page: int = Field(..., description="Current page number")
    per_page: int = Field(..., description="Number of items per page")
    total_pages: int = Field(..., description="Total number of pages")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "conversation_logs": [
                    {
                        "id": "550e8400-e29b-41d4-a716-************",
                        "lead_id": "550e8400-e29b-41d4-a716-************",
                        "sender": "user",
                        "message": "Hello",
                        "message_type": "text",
                        "platform": "sms",
                        "is_active": True,
                        "is_deleted": False,
                        "created_at": "2024-01-01T12:00:00Z",
                        "updated_at": "2024-01-01T12:00:00Z"
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 20,
                "total_pages": 1
            }
        }
    )
