"""
API Router Configuration
Main router that includes all API endpoints with proper documentation
"""

from fastapi import APIRouter

# Import all endpoint routers
from app.api.v1.endpoints import (
    auth,
    franchisors,
    leads,
    categories,
    holidays,
    messaging_rules,
    documents,
    logs,
    question_bank,
    general_messages,
    docqa,
    background_tasks,
    webhooks,
    agents,
    general_messages,
    zoho_simple,
    forgot_password,
    dashboard,
    dropdowns,
    prequalification,
    conversation_logs
)

# Create main API router
api_router = APIRouter()

# Include all endpoint routers with proper tags
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["Authentication"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        422: {"description": "Validation Error"}
    }
)

api_router.include_router(
    franchisors.router,
    prefix="/franchisors",
    tags=["Franchisors"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"}
    }
)



api_router.include_router(
    leads.router,
    prefix="/leads",
    tags=["Leads"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"}
    }
)



api_router.include_router(
    categories.router,
    prefix="",
    tags=["Industries"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"}
    }
)



api_router.include_router(
    documents.router,
    prefix="/documents",
    tags=["Documents"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"}
    }
)

# Settings endpoints
api_router.include_router(
    holidays.router,
    prefix="/settings",
    tags=["Settings - Holidays"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"}
    }
)

api_router.include_router(
    messaging_rules.router,
    prefix="/settings",
    tags=["Settings - Messaging Rules"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"}
    }
)

# Include logs router (no authentication required)
api_router.include_router(
    logs.router,
    prefix="/logs",
    tags=["Logs"],
    responses={
        404: {"description": "Log file not found"},
        500: {"description": "Internal server error"}
    }
)

api_router.include_router(
    question_bank.router,
    prefix="/questions",
    tags=["Questions"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"}
    }
)

# DocQA endpoints
api_router.include_router(
    docqa.router,
    prefix="/docqa",
    tags=["DocQA - Document Question Answering"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"},
        500: {"description": "Processing Error"}
    }
)

api_router.include_router(
    general_messages.router,
    prefix="/general-messages",
    tags=["General Messages"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"}
    }
)

# Background Tasks endpoints
api_router.include_router(
    background_tasks.router,
    prefix="",
    tags=["Background Tasks"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"},
        500: {"description": "Internal Server Error"}
    }
)

# Include webhook router (no authentication required)
api_router.include_router(
    webhooks.router,
    prefix="/webhooks",
    tags=["Webhooks"],
    responses={
        400: {"description": "Bad Request"},
        500: {"description": "Internal Server Error"}
    }
)

# Agent System endpoints
api_router.include_router(
    agents.router,
    tags=["Agent System"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        422: {"description": "Validation Error"},
        500: {"description": "Internal Server Error"}
    }
)

api_router.include_router(
    zoho_simple.router,
    tags=["Zoho CRM Sync"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"},
        500: {"description": "Sync operation failed"}
    }
)

api_router.include_router(
    forgot_password.router,
    tags=["Forgot Password"],
    responses={
        400: {"description": "Bad Request - Invalid OTP or Reset Code"},
        404: {"description": "User Not Found"},
        422: {"description": "Validation Error"},
        500: {"description": "Internal Server Error"}
    }
)

api_router.include_router(
    dashboard.router,
    prefix="/dashboard",
    tags=["Dashboard"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        422: {"description": "Validation Error"},
        500: {"description": "Internal Server Error"}
    }
)

api_router.include_router(
    dropdowns.router,
    tags=["Dropdowns"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        422: {"description": "Validation Error"},
        500: {"description": "Internal Server Error"}
    }
)

api_router.include_router(
    prequalification.router,
    tags=["Questions"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"},
        500: {"description": "Internal Server Error"}
    }
)

api_router.include_router(
    conversation_logs.router,
    prefix="/conversation-logs",
    tags=["Conversation Logs"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"},
        500: {"description": "Internal Server Error"}
    }
)

# Add root endpoint
@api_router.get(
    "/",
    tags=["api"],
    summary="API Root",
    description="Welcome to the GrowthHive API",
    responses={
        200: {
            "description": "Successful response",
            "content": {
                "application/json": {
                    "example": {
                        "message": "Welcome to GrowthHive API",
                        "version": "1.0.0",
                        "docs_url": "/docs",
                        "redoc_url": "/redoc"
                    }
                }
            }
        }
    }
)
async def root():
    """API root endpoint"""
    return {
        "message": "Welcome to GrowthHive API",
        "version": "1.0.0",
        "docs_url": "/docs",
        "redoc_url": "/redoc"
    }
