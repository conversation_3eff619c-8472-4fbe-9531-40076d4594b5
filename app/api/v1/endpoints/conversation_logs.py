"""
Conversation Log API endpoints
Handles CRUD operations for conversation logs
"""

from datetime import datetime
from typing import Optional, Dict, Any
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import JSONResponse
from app.schemas.conversation_log import (
    ConversationLogCreateRequest,
    ConversationLogUpdateRequest,
    ConversationLogResponse,
    ConversationLogListResponse
)
from app.services.conversation_log_service import ConversationLogService
from app.core.factory import get_conversation_log_service
from app.core.security.enhanced_auth_middleware import get_current_user
from app.core.response import create_success_response, create_error_response
from app.core.error_codes import ErrorCodes
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post(
    "/",
    response_model=ConversationLogResponse,
    summary="Create Conversation Log",
    description="Create a new conversation log entry (authenticated)",
    responses={
        201: {
            "description": "Conversation log created successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Conversation Log Created",
                            "description": "Conversation log entry created successfully"
                        },
                        "data": {
                            "id": "550e8400-e29b-41d4-a716-446655440000",
                            "lead_id": "550e8400-e29b-41d4-a716-446655440001",
                            "sender": "user",
                            "message": "Hello, I'm interested in franchise opportunities",
                            "message_type": "text",
                            "platform": "sms",
                            "is_active": True,
                            "created_at": "2024-01-01T12:00:00Z"
                        }
                    }
                }
            }
        },
        400: {"description": "Invalid request data"},
        401: {"description": "Authentication required"},
        500: {"description": "Internal server error"}
    }
)
async def create_conversation_log(
    conversation_log_data: ConversationLogCreateRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    conversation_log_service: ConversationLogService = Depends(get_conversation_log_service)
) -> JSONResponse:
    """
    Create a new conversation log entry
    
    Args:
        conversation_log_data: Conversation log creation data
        current_user: Current authenticated user
        conversation_log_service: Conversation log service
        
    Returns:
        JSONResponse: Created conversation log data
    """
    try:
        # Create conversation log
        conversation_log = await conversation_log_service.repository.create_conversation_log(conversation_log_data)
        
        # Convert to response
        response_data = await conversation_log_service._conversation_log_to_response(conversation_log)
        
        return create_success_response(
            data=response_data.model_dump(),
            message_title="Conversation Log Created",
            message_description="Conversation log entry created successfully",
            status_code=status.HTTP_201_CREATED
        )
        
    except Exception as e:
        logger.error(f"Error creating conversation log: {e}")
        return create_error_response(
            error_code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message_title="Creation Failed",
            message_description="Failed to create conversation log entry",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get(
    "/",
    response_model=ConversationLogListResponse,
    summary="Get Conversation Logs",
    description="Get conversation logs with filtering and pagination (authenticated)",
    responses={
        200: {
            "description": "Conversation logs retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Conversation Logs Retrieved",
                            "description": "Conversation logs retrieved successfully"
                        },
                        "data": {
                            "conversation_logs": [
                                {
                                    "id": "550e8400-e29b-41d4-a716-446655440000",
                                    "lead_id": "550e8400-e29b-41d4-a716-446655440001",
                                    "sender": "user",
                                    "message": "Hello",
                                    "message_type": "text",
                                    "platform": "sms",
                                    "is_active": True,
                                    "created_at": "2024-01-01T12:00:00Z"
                                }
                            ],
                            "total": 1,
                            "page": 1,
                            "per_page": 20,
                            "total_pages": 1
                        }
                    }
                }
            }
        },
        401: {"description": "Authentication required"},
        500: {"description": "Internal server error"}
    }
)
async def get_conversation_logs(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of records to return"),
    lead_id: Optional[str] = Query(None, description="Filter by lead ID"),
    sender: Optional[str] = Query(None, description="Filter by sender type"),
    platform: Optional[str] = Query(None, description="Filter by platform"),
    message_type: Optional[str] = Query(None, description="Filter by message type"),
    created_from: Optional[datetime] = Query(None, description="Filter by creation date from"),
    created_to: Optional[datetime] = Query(None, description="Filter by creation date to"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    conversation_log_service: ConversationLogService = Depends(get_conversation_log_service)
) -> JSONResponse:
    """
    Get conversation logs with filtering and pagination
    
    Args:
        skip: Number of records to skip
        limit: Maximum number of records to return
        lead_id: Filter by lead ID
        sender: Filter by sender type
        platform: Filter by platform
        message_type: Filter by message type
        created_from: Filter by creation date from
        created_to: Filter by creation date to
        current_user: Current authenticated user
        conversation_log_service: Conversation log service
        
    Returns:
        JSONResponse: List of conversation logs with pagination
    """
    try:
        # Convert lead_id to UUID if provided
        lead_uuid = None
        if lead_id:
            try:
                lead_uuid = UUID(lead_id)
            except ValueError:
                return create_error_response(
                    error_code=ErrorCodes.VALIDATION_ERROR,
                    message_title="Invalid Lead ID",
                    message_description="Lead ID must be a valid UUID",
                    status_code=status.HTTP_400_BAD_REQUEST
                )
        
        # Get conversation logs
        result = await conversation_log_service.get_conversation_logs(
            skip=skip,
            limit=limit,
            lead_id=lead_uuid,
            sender=sender,
            platform=platform,
            message_type=message_type,
            created_from=created_from,
            created_to=created_to
        )
        
        return create_success_response(
            data=result.model_dump(),
            message_title="Conversation Logs Retrieved",
            message_description="Conversation logs retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error getting conversation logs: {e}")
        return create_error_response(
            error_code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message_title="Retrieval Failed",
            message_description="Failed to retrieve conversation logs",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get(
    "/lead/{lead_id}",
    response_model=list[ConversationLogResponse],
    summary="Get Conversation Logs by Lead",
    description="Get conversation logs for a specific lead (authenticated)",
    responses={
        200: {
            "description": "Conversation logs retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Conversation Logs Retrieved",
                            "description": "Conversation logs for lead retrieved successfully"
                        },
                        "data": [
                            {
                                "id": "550e8400-e29b-41d4-a716-446655440000",
                                "lead_id": "550e8400-e29b-41d4-a716-446655440001",
                                "sender": "user",
                                "message": "Hello",
                                "message_type": "text",
                                "platform": "sms",
                                "is_active": True,
                                "created_at": "2024-01-01T12:00:00Z"
                            }
                        ]
                    }
                }
            }
        },
        400: {"description": "Invalid lead ID"},
        401: {"description": "Authentication required"},
        500: {"description": "Internal server error"}
    }
)
async def get_conversation_logs_by_lead(
    lead_id: str,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    conversation_log_service: ConversationLogService = Depends(get_conversation_log_service)
) -> JSONResponse:
    """
    Get conversation logs for a specific lead
    
    Args:
        lead_id: Lead ID to filter by
        skip: Number of records to skip
        limit: Maximum number of records to return
        current_user: Current authenticated user
        conversation_log_service: Conversation log service
        
    Returns:
        JSONResponse: List of conversation logs for the lead
    """
    try:
        # Convert lead_id to UUID
        try:
            lead_uuid = UUID(lead_id)
        except ValueError:
            return create_error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                message_title="Invalid Lead ID",
                message_description="Lead ID must be a valid UUID",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        
        # Get conversation logs
        conversation_logs = await conversation_log_service.get_conversation_logs_by_lead(
            lead_id=lead_uuid,
            skip=skip,
            limit=limit
        )
        
        return create_success_response(
            data=[log.model_dump() for log in conversation_logs],
            message_title="Conversation Logs Retrieved",
            message_description="Conversation logs for lead retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error getting conversation logs for lead {lead_id}: {e}")
        return create_error_response(
            error_code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message_title="Retrieval Failed",
            message_description="Failed to retrieve conversation logs for lead",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get(
    "/session/{session_id}",
    response_model=list[ConversationLogResponse],
    summary="Get Conversation Logs by Session",
    description="Get conversation logs for a specific session (authenticated)",
    responses={
        200: {
            "description": "Conversation logs retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Conversation Logs Retrieved",
                            "description": "Conversation logs for session retrieved successfully"
                        },
                        "data": [
                            {
                                "id": "550e8400-e29b-41d4-a716-446655440000",
                                "lead_id": "550e8400-e29b-41d4-a716-446655440001",
                                "sender": "user",
                                "message": "Hello",
                                "message_type": "text",
                                "platform": "sms",
                                "session_id": "sms_+1234567890",
                                "is_active": True,
                                "created_at": "2024-01-01T12:00:00Z"
                            }
                        ]
                    }
                }
            }
        },
        401: {"description": "Authentication required"},
        500: {"description": "Internal server error"}
    }
)
async def get_conversation_logs_by_session(
    session_id: str,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    conversation_log_service: ConversationLogService = Depends(get_conversation_log_service)
) -> JSONResponse:
    """
    Get conversation logs for a specific session

    Args:
        session_id: Session ID to filter by
        skip: Number of records to skip
        limit: Maximum number of records to return
        current_user: Current authenticated user
        conversation_log_service: Conversation log service

    Returns:
        JSONResponse: List of conversation logs for the session
    """
    try:
        # Get conversation logs
        conversation_logs = await conversation_log_service.get_conversation_logs_by_session(
            session_id=session_id,
            skip=skip,
            limit=limit
        )

        return create_success_response(
            data=[log.model_dump() for log in conversation_logs],
            message_title="Conversation Logs Retrieved",
            message_description="Conversation logs for session retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Error getting conversation logs for session {session_id}: {e}")
        return create_error_response(
            error_code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message_title="Retrieval Failed",
            message_description="Failed to retrieve conversation logs for session",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.delete(
    "/{conversation_log_id}",
    summary="Delete Conversation Log",
    description="Soft delete a conversation log entry (authenticated)",
    responses={
        200: {
            "description": "Conversation log deleted successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Conversation Log Deleted",
                            "description": "Conversation log entry deleted successfully"
                        },
                        "data": {
                            "deleted": True
                        }
                    }
                }
            }
        },
        400: {"description": "Invalid conversation log ID"},
        401: {"description": "Authentication required"},
        404: {"description": "Conversation log not found"},
        500: {"description": "Internal server error"}
    }
)
async def delete_conversation_log(
    conversation_log_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    conversation_log_service: ConversationLogService = Depends(get_conversation_log_service)
) -> JSONResponse:
    """
    Soft delete a conversation log entry

    Args:
        conversation_log_id: ID of the conversation log to delete
        current_user: Current authenticated user
        conversation_log_service: Conversation log service

    Returns:
        JSONResponse: Deletion confirmation
    """
    try:
        # Convert conversation_log_id to UUID
        try:
            log_uuid = UUID(conversation_log_id)
        except ValueError:
            return create_error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                message_title="Invalid Conversation Log ID",
                message_description="Conversation log ID must be a valid UUID",
                status_code=status.HTTP_400_BAD_REQUEST
            )

        # Delete conversation log
        success = await conversation_log_service.delete_conversation_log(log_uuid)

        return create_success_response(
            data={"deleted": success},
            message_title="Conversation Log Deleted",
            message_description="Conversation log entry deleted successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting conversation log {conversation_log_id}: {e}")
        return create_error_response(
            error_code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message_title="Deletion Failed",
            message_description="Failed to delete conversation log entry",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
