"""
Conversation Log Model
SQLAlchemy model for storing conversation messages between users and the system
"""

from datetime import datetime
from typing import Optional
from uuid import UUID, uuid4
from sqlalchemy import String, Boolean, DateTime, Text, ForeignKey, func, CheckConstraint
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.core.database.connection import Base


class ConversationLog(Base):
    """Conversation log model for storing SMS/chat messages between users and system"""

    __tablename__ = "conversation_logs"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PGUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        server_default=func.gen_random_uuid(),
    )

    # Lead association
    lead_id: Mapped[UUID] = mapped_column(
        PGUUID(as_uuid=True),
        ForeignKey("leads.id"),
        nullable=False,
        index=True,
        comment="Associated lead ID",
    )

    # Franchisor association (optional)
    franchisor_id: Mapped[Optional[UUID]] = mapped_column(
        PGUUID(as_uuid=True),
        ForeignKey("franchisors.id"),
        nullable=True,
        index=True,
        comment="Associated franchisor ID if available",
    )

    # Message details
    sender: Mapped[str] = mapped_column(
        String(10),
        nullable=False,
        index=True,
        comment="Message sender: 'user' or 'system'",
    )

    message: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        comment="Message content",
    )

    message_type: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        default="text",
        index=True,
        comment="Message type: 'text', 'media', etc.",
    )

    # Session context
    session_id: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        index=True,
        comment="Session identifier for grouping related messages",
    )

    phone_number: Mapped[Optional[str]] = mapped_column(
        String(20),
        nullable=True,
        index=True,
        comment="Phone number associated with the conversation",
    )

    platform: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        default="sms",
        index=True,
        comment="Platform where the message was sent/received (sms, whatsapp, etc.)",
    )

    # Metadata
    webhook_payload: Mapped[Optional[dict]] = mapped_column(
        Text,
        nullable=True,
        comment="JSON string of original webhook payload for debugging",
    )

    # Standard fields following the existing pattern
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        comment="Whether this conversation log record is active",
    )

    is_deleted: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        comment="Whether this conversation log record is deleted",
    )

    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="When this conversation log record was created",
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="When this conversation log record was last updated",
    )

    deleted_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="When this conversation log record was deleted",
    )

    # Relationships
    lead = relationship("Lead", back_populates="conversation_logs")
    franchisor = relationship("Franchisor", back_populates="conversation_logs")

    # Constraints
    __table_args__ = (
        CheckConstraint(
            "sender IN ('user', 'system')",
            name="check_sender_valid"
        ),
        CheckConstraint(
            "message_type IN ('text', 'media', 'image', 'video', 'audio', 'document')",
            name="check_message_type_valid"
        ),
    )

    def __repr__(self) -> str:
        return (
            f"<ConversationLog(id={self.id}, lead_id={self.lead_id}, "
            f"sender={self.sender}, message_type={self.message_type}, "
            f"platform={self.platform})>"
        )
