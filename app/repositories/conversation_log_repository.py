"""
Conversation Log Repository
Database operations for conversation log management
"""

from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, or_, desc, func
from sqlalchemy.orm import selectinload
from app.models.conversation_log import ConversationLog
from app.schemas.conversation_log import ConversationLogCreateRequest, ConversationLogUpdateRequest
from app.repositories.base import BaseRepository
import logging
import json

logger = logging.getLogger(__name__)


class ConversationLogRepository(BaseRepository[ConversationLog, ConversationLogCreateRequest, ConversationLogUpdateRequest]):
    """Repository for conversation log operations."""

    def __init__(self, db: AsyncSession):
        super().__init__(ConversationLog, db)

    async def create_conversation_log(self, conversation_log_data: ConversationLogCreateRequest) -> ConversationLog:
        """
        Create a new conversation log entry.
        
        Args:
            conversation_log_data: Conversation log creation data
            
        Returns:
            Created ConversationLog object
        """
        try:
            # Sanitize message content to prevent harmful characters
            sanitized_message = self._sanitize_message(conversation_log_data.message)
            
            # Create conversation log object
            conversation_log = ConversationLog(
                lead_id=conversation_log_data.lead_id,
                franchisor_id=conversation_log_data.franchisor_id,
                sender=conversation_log_data.sender.value,
                message=sanitized_message,
                message_type=conversation_log_data.message_type.value,
                session_id=conversation_log_data.session_id,
                phone_number=conversation_log_data.phone_number,
                platform=conversation_log_data.platform.value,
                webhook_payload=conversation_log_data.webhook_payload,
                is_active=True,
                is_deleted=False
            )
            
            self.db.add(conversation_log)
            await self.db.commit()
            await self.db.refresh(conversation_log)
            
            logger.info(f"Created conversation log: {conversation_log.id} for lead: {conversation_log.lead_id}")
            return conversation_log
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating conversation log: {e}")
            raise

    async def get_conversation_logs_by_lead(
        self,
        lead_id: UUID,
        skip: int = 0,
        limit: int = 100,
        include_deleted: bool = False
    ) -> List[ConversationLog]:
        """
        Get conversation logs for a specific lead.
        
        Args:
            lead_id: Lead ID to filter by
            skip: Number of records to skip
            limit: Maximum number of records to return
            include_deleted: Whether to include deleted records
            
        Returns:
            List of ConversationLog objects
        """
        try:
            query = select(ConversationLog).where(ConversationLog.lead_id == lead_id)
            
            if not include_deleted:
                query = query.where(ConversationLog.is_deleted == False)
            
            query = query.order_by(desc(ConversationLog.created_at)).offset(skip).limit(limit)
            
            result = await self.db.execute(query)
            conversation_logs = result.scalars().all()
            
            return list(conversation_logs)
            
        except Exception as e:
            logger.error(f"Error fetching conversation logs for lead {lead_id}: {e}")
            raise

    async def get_conversation_logs_by_session(
        self,
        session_id: str,
        skip: int = 0,
        limit: int = 100,
        include_deleted: bool = False
    ) -> List[ConversationLog]:
        """
        Get conversation logs for a specific session.
        
        Args:
            session_id: Session ID to filter by
            skip: Number of records to skip
            limit: Maximum number of records to return
            include_deleted: Whether to include deleted records
            
        Returns:
            List of ConversationLog objects
        """
        try:
            query = select(ConversationLog).where(ConversationLog.session_id == session_id)
            
            if not include_deleted:
                query = query.where(ConversationLog.is_deleted == False)
            
            query = query.order_by(ConversationLog.created_at).offset(skip).limit(limit)
            
            result = await self.db.execute(query)
            conversation_logs = result.scalars().all()
            
            return list(conversation_logs)
            
        except Exception as e:
            logger.error(f"Error fetching conversation logs for session {session_id}: {e}")
            raise

    async def get_conversation_logs(
        self,
        skip: int = 0,
        limit: int = 20,
        lead_id: Optional[UUID] = None,
        sender: Optional[str] = None,
        platform: Optional[str] = None,
        message_type: Optional[str] = None,
        created_from: Optional[datetime] = None,
        created_to: Optional[datetime] = None,
        include_deleted: bool = False
    ) -> tuple[List[ConversationLog], int]:
        """
        Get conversation logs with filtering and pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            lead_id: Filter by lead ID
            sender: Filter by sender type
            platform: Filter by platform
            message_type: Filter by message type
            created_from: Filter by creation date from
            created_to: Filter by creation date to
            include_deleted: Whether to include deleted records
            
        Returns:
            Tuple of (conversation logs list, total count)
        """
        try:
            # Build base query
            query = select(ConversationLog)
            count_query = select(func.count(ConversationLog.id))
            
            # Apply filters
            filters = []
            
            if not include_deleted:
                filters.append(ConversationLog.is_deleted == False)
            
            if lead_id:
                filters.append(ConversationLog.lead_id == lead_id)
            
            if sender:
                filters.append(ConversationLog.sender == sender)
            
            if platform:
                filters.append(ConversationLog.platform == platform)
            
            if message_type:
                filters.append(ConversationLog.message_type == message_type)
            
            if created_from:
                filters.append(ConversationLog.created_at >= created_from)
            
            if created_to:
                filters.append(ConversationLog.created_at <= created_to)
            
            if filters:
                query = query.where(and_(*filters))
                count_query = count_query.where(and_(*filters))
            
            # Get total count
            count_result = await self.db.execute(count_query)
            total = count_result.scalar()
            
            # Apply pagination and ordering
            query = query.order_by(desc(ConversationLog.created_at)).offset(skip).limit(limit)
            
            # Execute query
            result = await self.db.execute(query)
            conversation_logs = result.scalars().all()
            
            return list(conversation_logs), total
            
        except Exception as e:
            logger.error(f"Error fetching conversation logs: {e}")
            raise

    async def soft_delete_conversation_log(self, conversation_log_id: UUID) -> bool:
        """
        Soft delete a conversation log entry.
        
        Args:
            conversation_log_id: ID of the conversation log to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            query = update(ConversationLog).where(
                ConversationLog.id == conversation_log_id
            ).values(
                is_deleted=True,
                deleted_at=func.now()
            )
            
            result = await self.db.execute(query)
            await self.db.commit()
            
            return result.rowcount > 0
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error soft deleting conversation log {conversation_log_id}: {e}")
            raise

    def _sanitize_message(self, message: str) -> str:
        """
        Sanitize message content to prevent harmful characters.
        
        Args:
            message: Raw message content
            
        Returns:
            Sanitized message content
        """
        if not message:
            return ""
        
        # Remove null bytes and other control characters
        sanitized = message.replace('\x00', '').replace('\r\n', '\n').replace('\r', '\n')
        
        # Limit message length
        if len(sanitized) > 10000:
            sanitized = sanitized[:10000]
        
        return sanitized.strip()
